{"name": "test5", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F @test5/backend dev", "dev:setup": "turbo -F @test5/backend dev:setup"}, "devDependencies": {"@biomejs/biome": "^2.2.0", "turbo": "^2.5.4"}, "packageManager": "pnpm@10.17.0"}