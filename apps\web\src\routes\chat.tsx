import { createFileRoute } from "@tanstack/react-router";
import { api } from "@test5/backend/convex/_generated/api";

import { useMutation, useQuery } from "convex/react";
import { Toaster } from "../components/ui/toaster";
import { usePaginatedQuery } from "convex-helpers/react";
import {
    optimisticallySendMessage,
    toUIMessages,
    useThreadMessages,
    type UIMessage,
} from "@convex-dev/agent/react";
import { useState } from "react";
import { cn } from "../lib/utils";
import { useDemoThread } from "../components/hook/use-demo-thread";

export const Route = createFileRoute("/chat")({
    component: RouteComponent,
});

export default function RouteComponent() {
    const {
        threadId,
        resetThread: newThread,
        setThreadId,
    } = useDemoThread("Basic Chat Example");

    // Fetch thread title if threadId exists
    const threadDetails = useQuery(
        api.threads.getThreadDetails,
        threadId ? { threadId } : "skip"
    );

    // Fetch all threads
    const threads = usePaginatedQuery(
        api.threads.listThreads,
        {},
        { initialNumItems: 20 }
    );

    return (
        <div className="h-full flex flex-col">
            <header className="bg-white/80 backdrop-blur-sm p-4 flex justify-between items-center border-b">
                <div className="flex items-center gap-4">
                    <h1 className="text-xl font-semibold accent-text">
                        Basic Chat Example
                    </h1>
                    {threadId && threadDetails && threadDetails.title && (
                        <span
                            className="text-gray-500 text-base font-normal truncate max-w-xs"
                            title={threadDetails.title}
                        >
                            &mdash; {threadDetails.title}
                        </span>
                    )}
                </div>
            </header>
            <div className="h-full flex flex-row bg-gray-50 flex-1 min-h-0">
                {/* Sidebar */}
                <aside className="w-64 bg-white border-r flex flex-col h-full min-h-0">
                    <div className="p-4 border-b font-semibold text-lg">
                        Threads
                    </div>
                    <div className="flex-1 overflow-y-auto min-h-0">
                        {threads.results.length === 0 && (
                            <div className="p-4 text-gray-400 text-sm">
                                No threads yet.
                            </div>
                        )}
                        <ul>
                            {threads.results.map((thread) => (
                                <li key={thread._id}>
                                    <button
                                        className={cn(
                                            "w-full text-left px-4 py-2 hover:bg-blue-50 transition flex items-center gap-2",
                                            threadId === thread._id &&
                                                "bg-blue-100 text-blue-900 font-semibold"
                                        )}
                                        onClick={() => {
                                            window.location.hash = thread._id;
                                            setThreadId(thread._id);
                                        }}
                                    >
                                        <span className="truncate max-w-[10rem]">
                                            {thread.title || "Untitled thread"}
                                        </span>
                                    </button>
                                </li>
                            ))}
                        </ul>
                    </div>
                    <div className="px-4 py-2">
                        <button
                            onClick={() => void newThread()}
                            className="w-full flex justify-center items-center gap-2 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition font-semibold shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
                            type="button"
                        >
                            <span className="text-lg">+</span>
                            <span>New Thread</span>
                        </button>
                    </div>
                </aside>
                {/* Main chat area */}
                <main className="flex-1 flex flex-col items-center justify-center p-8 h-full min-h-0">
                    {threadId ? (
                        <Chat threadId={threadId} />
                    ) : (
                        <div className="text-center text-gray-500">
                            Loading...
                        </div>
                    )}
                </main>
                <Toaster />
            </div>
        </div>
    );
}

function Chat({ threadId }: { threadId: string }) {
    const messages = useThreadMessages(
        api.chat.basic.listThreadMessages,
        { threadId },
        { initialNumItems: 10 }
    );
    const sendMessage = useMutation(
        api.chat.basic.sendMessage
    ).withOptimisticUpdate(
        optimisticallySendMessage(api.chat.basic.listThreadMessages)
    );
    const [prompt, setPrompt] = useState("Yo yo yo");

    function onSendClicked() {
        const trimmedPrompt = prompt.trim();
        if (trimmedPrompt === "") return;
        void sendMessage({ threadId, prompt: trimmedPrompt }).catch(() =>
            setPrompt(prompt)
        );
        setPrompt("");
    }

    return (
        <>
            <div className="w-full max-w-2xl bg-white rounded-xl shadow-lg p-6 flex flex-col gap-6 h-full min-h-0 justify-end">
                {messages.status !== "Exhausted" &&
                    messages.results?.length > 0 && (
                        <div className="flex justify-center">
                            <button
                                className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition font-semibold disabled:opacity-50"
                                onClick={() => messages.loadMore(10)}
                                disabled={messages.status !== "CanLoadMore"}
                            >
                                Load More
                            </button>
                        </div>
                    )}
                {messages.results?.length > 0 && (
                    <div className="flex flex-col gap-4 overflow-y-auto mb-4 flex-1 min-h-0">
                        {toUIMessages(messages.results ?? []).map((m) => (
                            <Message key={m.key} message={m} />
                        ))}
                    </div>
                )}
                <form
                    className="flex gap-2 items-center"
                    onSubmit={(e) => {
                        e.preventDefault();
                        onSendClicked();
                    }}
                >
                    <input
                        type="text"
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="flex-1 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-gray-50"
                        placeholder="Ask me anything..."
                    />
                    <button
                        type="submit"
                        className="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition font-semibold disabled:opacity-50"
                        disabled={!prompt.trim()}
                    >
                        Send
                    </button>
                </form>
            </div>
        </>
    );
}

function Message({ message }: { message: UIMessage }) {
    const isUser = message.role === "user";
    return (
        <div className={`flex ${isUser ? "justify-end" : "justify-start"}`}>
            <div
                className={`rounded-lg px-4 py-2 max-w-lg whitespace-pre-wrap shadow-sm ${
                    isUser
                        ? "bg-blue-100 text-blue-900"
                        : "bg-gray-200 text-gray-800"
                }`}
            >
                {message.text || "..."}
            </div>
        </div>
    );
}
