{"name": "web", "private": true, "type": "module", "scripts": {"build": "vite build", "serve": "vite preview", "dev": "vite dev --port=3001"}, "dependencies": {"@convex-dev/agent": "^0.2.10", "@convex-dev/better-auth": "^0.8.4", "@convex-dev/react-query": "^0.0.0-alpha.8", "@radix-ui/react-toast": "^1.2.15", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-form": "^1.0.5", "@tanstack/react-query": "^5.80.6", "@tanstack/react-router": "^1.121.0-alpha.27", "@tanstack/react-router-with-query": "^1.121.0", "@tanstack/react-start": "^1.121.0-alpha.27", "@tanstack/router-plugin": "^1.121.0", "@test5/backend": "workspace:*", "better-auth": "1.3.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.27.0", "convex-helpers": "^0.1.104", "lucide-react": "^0.525.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "vite-tsconfig-paths": "^5.1.4", "zod": "^4.1.9"}, "devDependencies": {"@tanstack/react-router-devtools": "^1.121.0-alpha.27", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "~19.1.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^5.0.1", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^7.0.2", "web-vitals": "^5.0.3"}}