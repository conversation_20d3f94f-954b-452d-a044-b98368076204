import { components } from "./_generated/api";
import { Agent, createThread } from "@convex-dev/agent";
import { openai } from "@ai-sdk/openai";

import { action } from "./_generated/server";
import { v } from "convex/values";

const agent = new Agent(components.agent, {
    name: "My Agent",
    languageModel: openai.chat("gpt-5-mini"),
    instructions: "You are a weather forecaster.",
    maxSteps: 3,
});

export const helloWorld = action({
    args: { city: v.string() },
    handler: async (ctx, { city }) => {
        const threadId = await createThread(ctx, components.agent);
        const prompt = `What is the weather in ${city}?`;
        const result = await agent.generateText(ctx, { threadId }, { prompt });
        return result.text;
    },
});
