import { type Config } from "@convex-dev/agent";
import { rawRequestResponseHandler } from "../debugging/rawRequestResponseHandler";
import { usageHandler } from "../usage_tracking/usageHandler";
import { openai } from "@ai-sdk/openai";

export const defaultConfig = {
    languageModel: openai.chat("gpt-4o-mini"),
    rawRequestResponseHandler,
    usageHandler,
    callSettings: {
        temperature: 1.0,
    },
} satisfies Config;
